import fitz
import os
import subprocess
from typing import TypedDict, Annotated, List
from langgraph.graph.message import add_messages
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage, BaseMessage
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import InMemorySaver

# ---------------------- TOOLS ----------------------

import json
import uuid
import random
import string
from datetime import datetime

# User database (in production, use a real database)
USERS_DB_FILE = "users_db.json"

def load_users_db():
    """Load users database from file"""
    if os.path.exists(USERS_DB_FILE):
        try:
            with open(USERS_DB_FILE, 'r') as f:
                return json.load(f)
        except:
            return {}
    return {}

def save_users_db(users_db):
    """Save users database to file"""
    with open(USERS_DB_FILE, 'w') as f:
        json.dump(users_db, f, indent=2)

@tool
def auto_generate_user_id(name_hint: str = "") -> str:
    """
    Automatically generate a unique user ID for deployment.
    This runs in the background when user wants to publish.
    
    Args:
        name_hint: Optional hint from user content (like name from resume)
    
    Returns:
        Generated user ID
    """
    users_db = load_users_db()
    
    if name_hint:
        # Create ID from name hint
        base_name = name_hint.split()[0].lower().replace(" ", "")[:8]  # First 8 chars of first name
        random_num = random.randint(100, 999)
        user_id = f"{base_name}{random_num}"
    else:
        # Generate random user ID
        user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    
    # Ensure uniqueness
    while user_id in users_db:
        random_num = random.randint(100, 999)
        if name_hint:
            user_id = f"{base_name}{random_num}"
        else:
            user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    
    # Create simple user profile
    user_profile = {
        "user_id": user_id,
        "created_at": datetime.now().isoformat(),
        "projects": [],
        "last_activity": datetime.now().isoformat()
    }
    
    # Save to database
    users_db[user_id] = user_profile
    save_users_db(users_db)
    
    return user_id

@tool
def process_resume_file(file_path: str) -> str:
    """
    Process uploaded resume/CV file to extract information for portfolio website.
    Supports PDF and text files.
    
    Args:
        file_path: Path to the uploaded resume file
    
    Returns:
        Extracted information formatted for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        else:
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        
        # Basic information extraction (you can enhance this with NLP)
        info = {
            "raw_text": text,
            "length": len(text),
            "has_email": "@" in text,
            "has_phone": any(char.isdigit() for char in text),
            "sections": []
        }
        
        # Try to identify common resume sections
        common_sections = [
            "experience", "education", "skills", "projects", 
            "summary", "objective", "certifications", "achievements"
        ]
        
        text_lower = text.lower()
        for section in common_sections:
            if section in text_lower:
                info["sections"].append(section)
        
        result = f"📄 Resume/CV processed successfully!\n\n"
        result += f"📊 Content Analysis:\n"
        result += f"   - Length: {info['length']} characters\n"
        result += f"   - Contains email: {'✅' if info['has_email'] else '❌'}\n"
        result += f"   - Contains phone: {'✅' if info['has_phone'] else '❌'}\n"
        result += f"   - Detected sections: {', '.join(info['sections']) if info['sections'] else 'None detected'}\n\n"
        result += f"📝 Raw content:\n{text[:500]}{'...' if len(text) > 500 else ''}\n\n"
        result += f"Now I'll create a professional portfolio website based on this information!"
        
        return result
        
    except Exception as e:
        return f"❌ Error processing resume file: {str(e)}"

@tool
def process_instruction_file(file_path: str) -> str:
    """
    Process uploaded instruction file for specific website requirements.
    
    Args:
        file_path: Path to the uploaded instruction file
    
    Returns:
        Processed instructions for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        else:
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        
        result = f"📋 Instructions processed successfully!\n\n"
        result += f"📝 Your requirements:\n{text}\n\n"
        result += f"I'll create a website following these specific instructions!"
        
        return result
        
    except Exception as e:
        return f"❌ Error processing instruction file: {str(e)}"

@tool
def deploy_html_to_cloudflare(html_content: str, name_hint: str = "") -> str:
    """
    Deploy HTML to Cloudflare Pages with auto-generated user ID.
    
    Args:
        html_content: The HTML content to deploy
        name_hint: Optional hint for generating user ID (from resume or user input)
    
    Returns:
        Deployment confirmation with generated URL
    """
    # Auto-generate user ID
    user_id = auto_generate_user_id(name_hint)
    
    base_dir = "agent-pages"
    public_dir = os.path.join(base_dir, "public", user_id)
    index_path = os.path.join(public_dir, "index.html")
    project_name = "agent-pages"
    cloudflare_url = f"https://{project_name}.pages.dev/{user_id}/"

    os.makedirs(public_dir, exist_ok=True)
    with open(index_path, "w", encoding="utf-8") as f:
        f.write(html_content)

    try:
        subprocess.run(
            ["wrangler", "pages", "publish", "public", "--project-name", project_name],
            cwd=base_dir,
            check=True
        )
        
        # Update user's project history
        users_db = load_users_db()
        if user_id in users_db:
            project = {
                "name": f"Website Deployment",
                "url": cloudflare_url,
                "created_at": datetime.now().isoformat()
            }
            users_db[user_id]["projects"].append(project)
            users_db[user_id]["last_activity"] = datetime.now().isoformat()
            save_users_db(users_db)
        
    except subprocess.CalledProcessError as e:
        return f"❌ Deployment failed: {e}"

    return f"✅ Website deployed successfully!\n🌐 Your URL: {cloudflare_url}\n📝 Your unique ID: {user_id} (save this for future reference)"

# ---------------------- TOOLS LIST ----------------------

tools = [
    auto_generate_user_id,
    process_resume_file, 
    process_instruction_file,
    deploy_html_to_cloudflare
]

# ---------------------- AGENT STATE ----------------------

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    html_content: str
    user_satisfied: bool
    awaiting_publish_confirmation: bool
    user_id: str
    resume_processed: bool
    website_created: bool

# ---------------------- LLM ----------------------

llm = ChatGoogleGenerativeAI(
    model=os.getenv("GOOGLE_MODEL", "gemini-pro"),
    google_api_key=os.getenv("GOOGLE_API_KEY")
).bind_tools(tools)

# Enhanced system prompt for proper workflow
DEFAULT_PROMPT = """
You are an expert frontend developer AI assistant that helps users create beautiful, responsive HTML pages.

Your workflow:
1. Ask the user to either:
   - Upload their resume/CV file for a portfolio website
   - Upload instruction files for specific requirements
   - Directly describe what kind of website they want

2. Process their content/instructions using the appropriate tools

3. Generate a complete, professional HTML5 page with:
   - Modern HTML5 semantic elements
   - Responsive design with CSS Grid/Flexbox
   - Clean, embedded CSS styling
   - Professional layout and design
   - Full <html>, <head>, and <body> structure

4. Show the user the HTML code and ask for their feedback

5. Make revisions based on user feedback if needed

6. Once satisfied, ask if they want to publish it

7. Only deploy when user explicitly confirms

IMPORTANT RULES:
- Always show the complete HTML code after creating it
- Always ask "Are you satisfied with this website?" before offering deployment
- Never deploy without explicit user confirmation
- Only use the deploy_html_to_cloudflare tool when user says "yes" to publishing
- Create complete, functional HTML pages, not just snippets
- Use modern CSS techniques and responsive design
- Make the website professional and visually appealing

When creating HTML, always include:
- Proper DOCTYPE and meta tags
- Responsive viewport meta tag
- Semantic HTML5 elements
- Clean, modern CSS styling
- Professional color scheme and typography
- Mobile-responsive design

After creating the HTML, always ask: "Are you satisfied with this website? Would you like me to make any changes, or are you ready to publish it?"
"""

# ---------------------- USER INPUT HANDLER ----------------------

def get_user_input(prompt: str = "Your input: ") -> str:
    """Get user input from console"""
    return input(f"\n{prompt}")

# ---------------------- LLM MODEL CALL NODE ----------------------

def llm_model_call(state: AgentState) -> AgentState:
    print("[llm_model_call] Invoking LLM with state")
    
    system_prompt = os.getenv("SYSTEM_PROMPT", DEFAULT_PROMPT)

    valid_messages = [msg for msg in state["messages"] if getattr(msg, "content", "").strip()]
    response = llm.invoke([
        SystemMessage(content=system_prompt),
        *valid_messages
    ])
    
    print(f"\n🤖 Assistant: {response.content}")
    
    # Check if response contains HTML (look for complete HTML structure)
    has_html = "<html>" in response.content and "</html>" in response.content
    
    # Check if asking for satisfaction
    asking_satisfaction = any(phrase in response.content.lower() for phrase in [
        "satisfied", "happy with", "ready to publish", "want to publish", 
        "are you satisfied", "would you like me to make any changes"
    ])
    
    return {
        "messages": state["messages"] + [response],
        "html_content": response.content if has_html else state.get("html_content", ""),
        "user_satisfied": state.get("user_satisfied", False),
        "awaiting_publish_confirmation": asking_satisfaction and has_html,
        "user_id": state.get("user_id", ""),
        "resume_processed": state.get("resume_processed", False),
        "website_created": has_html or state.get("website_created", False)
    }

# ---------------------- USER INTERACTION NODE ----------------------

def user_interaction_node(state: AgentState) -> AgentState:
    """Handle user input and determine next steps"""
    print("[user_interaction_node] Getting user input")
    
    # Get user input
    user_input = get_user_input("👤 You: ")
    
    # Check for exit conditions
    if user_input.lower() in ["quit", "exit", "bye", "goodbye"]:
        return {
            "messages": state["messages"] + [HumanMessage(content=user_input)],
            "html_content": state.get("html_content", ""),
            "user_satisfied": False,
            "awaiting_publish_confirmation": False,
            "user_id": state.get("user_id", ""),
            "resume_processed": state.get("resume_processed", False),
            "website_created": state.get("website_created", False)
        }
    
    # Check for satisfaction/publishing responses
    satisfaction_keywords = ["satisfied", "happy", "good", "perfect", "looks good", "great"]
    publish_keywords = ["publish", "deploy", "go live", "yes", "yeah", "sure"]
    change_keywords = ["change", "modify", "edit", "different", "no", "not satisfied"]
    
    user_satisfied = any(keyword in user_input.lower() for keyword in satisfaction_keywords)
    wants_to_publish = any(keyword in user_input.lower() for keyword in publish_keywords)
    wants_changes = any(keyword in user_input.lower() for keyword in change_keywords)
    
    # Determine publishing intent
    if state.get("awaiting_publish_confirmation"):
        if wants_to_publish and not wants_changes:
            user_satisfied = True
        elif wants_changes:
            user_satisfied = False
    
    # Create user message
    user_message = HumanMessage(content=user_input)
    
    return {
        "messages": state["messages"] + [user_message],
        "html_content": state.get("html_content", ""),
        "user_satisfied": user_satisfied,
        "awaiting_publish_confirmation": state.get("awaiting_publish_confirmation", False),
        "user_id": state.get("user_id", ""),
        "resume_processed": state.get("resume_processed", False),
        "website_created": state.get("website_created", False)
    }

# ---------------------- TOOL NODE ----------------------

tool_node = ToolNode(tools)

# ---------------------- DECISION FUNCTIONS ----------------------

def should_continue(state: AgentState) -> str:
    """Determine next step based on state"""
    last_message = state["messages"][-1]
    
    # Check if LLM wants to use a tool
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        tool_name = last_message.tool_calls[0]["name"]
        print(f"[should_continue] Detected tool call: {tool_name}")
        
        # Only allow deployment if user is satisfied
        if tool_name == "deploy_html_to_cloudflare":
            if state.get("user_satisfied") and state.get("awaiting_publish_confirmation"):
                return "tool_node"
            else:
                print("[should_continue] Blocking deployment - user not satisfied or not awaiting confirmation")
                return "continue"
        
        return "tool_node"
    
    # Check if we should deploy based on user satisfaction
    if (state.get("user_satisfied") and 
        state.get("awaiting_publish_confirmation") and 
        state.get("html_content") and
        isinstance(last_message, HumanMessage)):
        
        user_input = last_message.content.lower()
        if any(keyword in user_input for keyword in ["yes", "publish", "deploy", "go live", "sure"]):
            return "deploy"
    
    # Continue conversation
    return "continue"

def deploy_website(state: AgentState) -> AgentState:
    """Deploy the website"""
    print("[deploy_website] Deploying website")
    
    html_content = state.get("html_content", "")
    
    if not html_content:
        error_msg = "❌ No HTML content found to deploy."
        return {
            "messages": state["messages"] + [HumanMessage(content=error_msg)],
            "html_content": state.get("html_content", ""),
            "user_satisfied": False,
            "awaiting_publish_confirmation": False,
            "user_id": state.get("user_id", ""),
            "resume_processed": state.get("resume_processed", False),
            "website_created": state.get("website_created", False)
        }
    
    # Extract name hint from messages for user ID generation
    name_hint = ""
    for message in state["messages"]:
        if hasattr(message, 'content') and message.content:
            content = message.content.lower()
            if "name" in content or "cv" in content or "resume" in content:
                # Simple name extraction - enhance as needed
                words = message.content.split()
                for i, word in enumerate(words):
                    if word.lower() in ["name", "i'm", "i am"] and i + 1 < len(words):
                        name_hint = words[i + 1]
                        break
                if name_hint:
                    break
    
    # Create deployment message that will trigger the tool
    deploy_message = HumanMessage(content=f"Deploy this website: {html_content[:100]}...")
    
    # Invoke LLM to use deployment tool
    deploy_system_prompt = f"""Use the deploy_html_to_cloudflare tool to deploy the HTML content. 
    HTML content: {html_content}
    Name hint: {name_hint}
    
    Call the tool with the full HTML content."""
    
    response = llm.invoke([
        SystemMessage(content=deploy_system_prompt),
        deploy_message
    ])
    
    return {
        "messages": state["messages"] + [response],
        "html_content": html_content,
        "user_satisfied": True,
        "awaiting_publish_confirmation": False,
        "user_id": state.get("user_id", ""),
        "resume_processed": state.get("resume_processed", False),
        "website_created": state.get("website_created", False)
    }

# ---------------------- GRAPH BUILD ----------------------

graph_builder = StateGraph(AgentState)

# Add nodes
graph_builder.add_node("llm_call", llm_model_call)
graph_builder.add_node("user_input", user_interaction_node)
graph_builder.add_node("tool_node", tool_node)
graph_builder.add_node("deploy", deploy_website)

# Set entry point
graph_builder.set_entry_point("llm_call")

# Add edges
graph_builder.add_conditional_edges(
    "llm_call",
    should_continue,
    {
        "tool_node": "tool_node",
        "deploy": "deploy",
        "continue": "user_input"
    }
)

graph_builder.add_edge("user_input", "llm_call")
graph_builder.add_edge("tool_node", "llm_call")
graph_builder.add_edge("deploy", "llm_call")

# Compile graph
memory = InMemorySaver()
graph = graph_builder.compile(checkpointer=memory)

# ---------------------- EXECUTION ----------------------

def run_interactive_agent():
    """Run the interactive agent"""
    print("🚀 Welcome to the Interactive Web Developer Agent!")
    print("I'll help you create and publish beautiful webpages.")
    print("Type 'quit' or 'exit' to stop the conversation.\n")
    
    # Thread configuration for memory
    config = {"configurable": {"thread_id": "user_session_1"}}
    
    # Initial state
    initial_state = {
        "messages": [HumanMessage(content="Hello! I'm your personal web developer. I can help you create a beautiful website in 3 easy ways:\n\n1. 📄 Upload your resume/CV - I'll create a professional portfolio\n2. 📋 Upload instruction file - I'll follow your specific requirements\n3. 💬 Just tell me what you want - Describe your ideal website\n\nWhat would you like to do?")],
        "html_content": "",
        "user_satisfied": False,
        "awaiting_publish_confirmation": False,
        "user_id": "",
        "resume_processed": False,
        "website_created": False
    }
    
    # Start the conversation
    current_state = initial_state
    
    try:
        while True:
            # Invoke the graph
            result = graph.invoke(current_state, config=config)
            
            # Update current state
            current_state = result
            
            # Check for exit conditions
            if result["messages"]:
                last_message = result["messages"][-1]
                if hasattr(last_message, 'content'):
                    content = last_message.content.lower()
                    if any(exit_word in content for exit_word in ["quit", "exit", "bye", "goodbye"]):
                        print("\n👋 Thanks for using the Interactive Web Developer Agent!")
                        break
            
            # Check if we've successfully deployed
            if any("✅ Website deployed successfully!" in str(msg) for msg in result.get("messages", [])):
                print("\n🎉 Congratulations! Your website has been successfully published!")
                break
                
    except KeyboardInterrupt:
        print("\n\n👋 Thanks for using the Interactive Web Developer Agent!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")

if __name__ == "__main__":
    run_interactive_agent()